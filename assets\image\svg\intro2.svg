<svg xmlns="http://www.w3.org/2000/svg" width="250" height="250.197" viewBox="0 0 250 250.197">
  <g id="Group_4532" data-name="Group 4532" transform="translate(-63 -154)">
    <rect id="Rectangle_1724" data-name="Rectangle 1724" width="250" height="250" transform="translate(63 154)" fill="#fff" opacity="0"/>
    <path id="Path_7823" data-name="Path 7823" d="M333.7,242.178c.062.354.123.711.18,1.074.08.841.148,1.706.205,2.572s.1,1.739.139,2.594c-.028.858-.055,1.7-.082,2.514-.044.809-.085,1.586-.124,2.316-.034.552-.115,1.063-.19,1.549a81.607,81.607,0,0,0-3.944-26.971c-6.943-21.648-15.469-41.47-25.949-58q2.62,3.656,5.088,7.435c.987.051-4.555-9.555-.725-4.412,1.678,2.577,1.189,2.56,1.358,3.188.846,1.279,1.6,2.609,2.4,3.914s1.6,2.619,2.333,3.962l-.3,3.13c2.581,7.178,5.518,12.438,8.6,18.334a111.671,111.671,0,0,1,9.2,21.8,7.469,7.469,0,0,0,.767,1.025,11.376,11.376,0,0,0,0,2.74c.166,1.28.335,2.986.722,5.224.1.558.214,1.151.331,1.782q.139.953.294,2.022c.2,1.427.552,3,.725,4.784C334.29,242.609,334.088,243.87,333.7,242.178Zm-34.022-78.719q-2.609-3.706-5.369-7.145l.6.592c.589.6,1.144,1.258,1.72,1.935a27.783,27.783,0,0,1,3.306,4.7C299.983,163.7,299.864,163.626,299.673,163.459Zm-63.134-41.684c.28-.5.45-.989,2.761-1.068a38.369,38.369,0,0,1,3.763.869,37.049,37.049,0,0,1,3.989,1.168c2.425.891,4.144,1.645,3.492,1.8.471.221.951.439,1.431.656A77.739,77.739,0,0,0,236.539,121.775ZM184.432,336.9c-2.654-.314-4.934-.471-6.263-.51-1.783-.679-2.355-1.093-2.332-1.369q4.387,1.067,8.945,1.914C184.668,336.925,184.544,336.919,184.432,336.9Zm-40.5-188.514c-1.563,1.081-3.029,2.109-4.4,3.117-12.38,8.108-21.74,16.969-28.768,26.819.515-.772,1.016-1.558,1.58-2.3l2.006-2.7c1.42-1.739,2.817-3.523,4.38-5.17,1.5-1.708,3.145-3.291,4.774-4.9.832-.787,1.7-1.543,2.548-2.316l1.288-1.15,1.335-1.1A128.6,128.6,0,0,1,152,143.485C149.053,145.091,146.385,146.795,143.929,148.39Zm195.016,99.231c-.109-1.359-.236-2.565-.377-3.663a21.847,21.847,0,0,0-.565-2.994c-.222-.915-.45-1.765-.686-2.594-.938-3.037-2.054-5.79-3.736-10.357a10.7,10.7,0,0,0-.095-2.434c-.338-2.753-1.13-6.7-1.317-8.678l.158,1.983A29.484,29.484,0,0,1,328.857,211c-1.43-4.813,1.637,2.274.825-1.115a81.825,81.825,0,0,0-6.543-15.675c-2.105-3.842-4.262-7.324-7.495-13.351-1.294-2.784-2.522-5.436-3.949-7.868-.7-1.224-1.383-2.421-2.094-3.58q-1.11-1.715-2.225-3.353l-1.117-1.625c-.382-.531-.785-1.046-1.177-1.563l-2.373-3.047c-.792-1-1.662-1.946-2.492-2.911-.854-.948-1.655-1.93-2.56-2.84a160.654,160.654,0,0,0-26.024-22.017,103.107,103.107,0,0,1,11.221,9.85l1.439,1.422,1.4,1.523c.938,1.037,1.94,2.06,2.907,3.169l2.951,3.45c1.016,1.179,1.973,2.467,3.02,3.746-2.519-2.647-5.174-5.15-7.9-7.541a93.609,93.609,0,0,0-20.494-16.109c.3.156.591.316.885.465l2.866,1.488c1.814.971,3.364,1.975,4.668,2.775-.794-.768-1.741-1.627-2.777-2.536a19.511,19.511,0,0,0-1.649-1.334l-1.766-1.324-1.8-1.33-1.845-1.194-1.768-1.155c-.568-.381-1.116-.745-1.657-1.044-4.254-2.506-6.525-4.182-1.47-3.251-.733-.346-1.426-.684-2.095-.978l-1.949-.737c-1.238-.458-2.355-.856-3.384-1.2-2.1-.555-3.829-.943-5.439-1.261-1.628-.238-3.146-.369-4.8-.545l-1.27-.13-1.359-.056-3.048-.161c-.553-.087-1.157-.213-1.8-.265l-2.012-.143c-1.383-.078-2.822-.2-4.152-.239-2.66-.013-4.9-.093-5.439-.466-2.038.3-3.509.592-4.776.787-.633.1-1.214.19-1.786.266l-1.719.29c-1.186.179-2.508.326-4.313.484-1.8.189-4.046.538-7.136.924-2.056.542-4.06,1.17-5.834,1.732l-1.282.419-1.152.46c-.729.3-1.375.573-1.915.817-2.157.98-2.6,1.464.177.87a119.039,119.039,0,0,1,22.786-5.008c2.81.121,3.547.445,3.147.781a4.321,4.321,0,0,1-1.336.558c-.654.188-1.476.39-2.346.667-1.746.514-3.718,1.038-5.045,1.46-.2.076-.386.148-.556.217a125.681,125.681,0,0,0-19.553,5.105,296.008,296.008,0,0,0-27.676,11c2.6-1.47,5.241-2.9,7.889-4.309-4.962,2.122-9.917,4.277-14.735,6.73,1.05-1.168,3.3-3.054,3.824-3.95s-.772-1-6.766,1.693c-4.54,2.813-8.93,5.665-13.115,8.76-1.057.753-2.105,1.515-3.123,2.321l-3.075,2.37c-2,1.645-4.023,3.25-5.96,4.979A144.577,144.577,0,0,0,105.936,180.8a93.081,93.081,0,0,0-9.9,20.464,124.863,124.863,0,0,0-5.325,22.373c-.28,1.907-.512,3.824-.745,5.743-.18,1.923-.414,3.848-.542,5.781s-.308,3.856-.335,5.854L89,243.994c.011.993.061,1.989.091,2.984a75.737,75.737,0,0,0,4.564,23.555c-.421-2.7.283-1.053.765.032.243.545.431.953.4.622a10.778,10.778,0,0,0-.23-1.126c-.075-.3-.166-.673-.275-1.117-.087-.451-.189-.977-.307-1.589.241.56.453,1.112.7,1.651s.5,1.066.755,1.6c.115.24.224.484.333.73a79.5,79.5,0,0,0,5.034,11.46c-.371-.434-.7-.823-.941-1.132a8.607,8.607,0,0,0-.772-.839c-.384-.346-.516-.281-.467.087a10.151,10.151,0,0,0,.621,1.891c.379.835.895,1.842,1.443,2.921l.848,1.654L102.5,289c.619,1.062,1.177,2.1,1.647,2.96.983,1.689,1.428,2.824.571,2.556l1.63,2.17c.534.659,1.027,1.282,1.483,1.877.91,1.193,1.676,2.273,2.425,3.215.729.96,1.363,1.854,1.953,2.72s1.2,1.641,1.785,2.464,1.185,1.654,1.868,2.509c.7.835,1.467,1.717,2.355,2.679a35.61,35.61,0,0,0,3.173,3.05c.619.545,1.284,1.115,2,1.716.735.575,1.538,1.159,2.4,1.783,4.456,2.428,2.668.485-.686-2.483-.853-.725-1.727-1.627-2.672-2.487-.928-.881-1.916-1.74-2.785-2.664l-2.487-2.534c-.755-.763-1.345-1.5-1.817-2.043l4.322,4.132c-.585-.664-1.153-1.329-1.7-1.986l-.793-.978-.709-1a41.448,41.448,0,0,1-2.28-3.624,10.843,10.843,0,0,1-1.09-2.944,104.893,104.893,0,0,0,23.27,18.965c-.362.987-.391,1.993,3.093,4.011a16.7,16.7,0,0,0,4.122,2.763c.986.566,2.143,1.046,3.33,1.6,1.2.52,2.428,1.125,3.71,1.6l3.756,1.475c1.205.5,2.395.878,3.442,1.3,1.054.409,1.989.8,2.75,1.173a8.161,8.161,0,0,1,1.719.969c8.549,2.394,16.124,4.591,22.77,6.044a72.26,72.26,0,0,0,9.209,1.4,37.913,37.913,0,0,0,4,.107,23.736,23.736,0,0,0,3.567-.467l3.194.813,3.217.706,3.236.707,3.252.592c1.317.094,2.581.165,3.8.217s2.39.087,3.526.038c2.27-.063,4.382-.184,6.394-.338,2.012-.178,3.922-.495,5.793-.759l2.784-.42c.923-.148,1.84-.354,2.764-.523,1.632-.629-.421-.715-1.767-.916s-1.987-.516,2.36-1.586l2.74-.47c1.007-.158,2.083-.316,3.148-.528a39.858,39.858,0,0,1,5.849-.707c.93.368-3.144,1.4-4.052,1.708l3.092-.573,1.507-.256,1.47-.309c1.946-.4,3.831-.75,5.671-1.061,3.635-.814,7.125-1.354,10.44-2.167,3.359-.666,6.541-1.555,9.748-2.4,1.591-.453,3.147-1,4.73-1.522.787-.27,1.585-.522,2.37-.821l2.335-.974c7.9-4.324,17.065-10.277,24.1-17.368l2.576-2.634c.858-.852,1.58-1.809,2.346-2.686.742-.9,1.5-1.751,2.164-2.641l1.875-2.634a44.9,44.9,0,0,0,3.022-4.922,27.193,27.193,0,0,0,1.944-4.23L326.1,294.64c-.484.737-.994,1.457-1.489,2.186l-1.5,2.175c-.5.721-1.077,1.39-1.613,2.086.565-.779,1.081-1.446,1.472-2.075s.706-1.184.973-1.664a14.952,14.952,0,0,0,1.021-2.128c.354-.978.128-1.228-.047-1.514s-.3-.609.158-1.758a24.444,24.444,0,0,1,1.192-2.442c.6-1.081,1.249-2.556,2.232-4.418q.285-.519.544-.991l.44-.923.776-1.572c.472-.932.857-1.64,1.173-2.168.558-1.084.888-1.433,1.112-1.424.448.016.464,1.483,1.058,1.244.291-.86.591-1.8.9-2.785l.46-1.519c.12-.527.241-1.065.365-1.61l.734-3.342c.212-1.137.356-2.3.538-3.434.41-2.272.526-4.529.777-6.552.144-1.011.167-1.975.251-2.861s.165-1.7.273-2.413c-.066,4.133.259,2.558.584.4.076-.539.172-1.115.225-1.643s.084-1.012.113-1.366c.068-.71.118-.9.147.08C338.989,250.5,339.038,248.98,338.945,247.621Z" transform="translate(-26 60.076)" fill="#e7e7e7" fill-rule="evenodd" opacity="0.52"/>
    <path id="Path_7880" data-name="Path 7880" d="M347.616,443.454H226.037a5.114,5.114,0,0,1-5.117-5.112V320.889H352.732V438.342A5.114,5.114,0,0,1,347.616,443.454Z" transform="translate(-112.839 -59.458)" fill="#fff" fill-rule="evenodd"/>
    <path id="Path_7881" data-name="Path 7881" d="M345.641,242.817H228.01a7.088,7.088,0,0,0-7.09,7.084v20.217H352.732V249.9A7.087,7.087,0,0,0,345.641,242.817Z" transform="translate(-112.839 -8.686)" fill="#fff" fill-rule="evenodd"/>
    <path id="Path_7882" data-name="Path 7882" d="M402.454,266.726a8.155,8.155,0,1,1-8.155-8.147A8.151,8.151,0,0,1,402.454,266.726Z" transform="translate(-220.287 -18.937)" fill="#f8f8f8" fill-rule="evenodd"/>
    <path id="Path_7883" data-name="Path 7883" d="M517.849,266.726a8.155,8.155,0,1,1-8.155-8.147A8.151,8.151,0,0,1,517.849,266.726Z" transform="translate(-295.33 -18.937)" fill="#cfcfcf" fill-rule="evenodd" opacity="0.2"/>
    <path id="Path_7884" data-name="Path 7884" d="M288.329,266.726a8.155,8.155,0,1,1-8.155-8.147A8.151,8.151,0,0,1,288.329,266.726Z" transform="translate(-146.07 -18.937)" fill="#f8f8f8" fill-rule="evenodd"/>
    <path id="Path_7885" data-name="Path 7885" d="M291.239,220.078a4.771,4.771,0,0,0-9.541,0v18.307a4.771,4.771,0,0,0,9.541,0V223.062C291.239,223.055,291.239,220.085,291.239,220.078Z" transform="translate(-152.364 9.201)" fill="#fff" fill-rule="evenodd"/>
    <path id="Path_7886" data-name="Path 7886" d="M405.216,220.078a4.771,4.771,0,0,0-9.541,0v18.307a4.771,4.771,0,0,0,9.541,0V223.062C405.216,223.055,405.216,220.085,405.216,220.078Z" transform="translate(-226.485 9.201)" fill="#fff" fill-rule="evenodd"/>
    <path id="Path_7887" data-name="Path 7887" d="M520.48,220.078a4.771,4.771,0,0,0-9.542,0v18.307a4.771,4.771,0,0,0,9.542,0V223.062C520.478,223.055,520.48,220.085,520.48,220.078Z" transform="translate(-301.443 9.201)" fill="#fff" fill-rule="evenodd"/>
    <path id="Path_7888" data-name="Path 7888" d="M376.932,486.41H399.6V463.762H376.932Z" transform="translate(-214.296 -152.371)" fill="#fff" fill-rule="evenodd"/>
    <path id="Path_7889" data-name="Path 7889" d="M467.521,486.41h22.668V463.762H467.521Z" transform="translate(-273.208 -152.371)" fill="#fff" fill-rule="evenodd"/>
    <path id="Path_7890" data-name="Path 7890" d="M729.323,493.515l-8.616-20.947,20.967-8.608,8.616,20.947Z" transform="translate(-437.859 -152.499)" fill="#fff" fill-rule="evenodd"/>
    <path id="Path_7891" data-name="Path 7891" d="M285.962,463.763h22.669V486.41H297.915" transform="translate(-155.137 -152.371)" fill="#fff" fill-rule="evenodd"/>
    <path id="Path_7892" data-name="Path 7892" d="M467.521,396.281h22.668V373.633H467.521Z" transform="translate(-273.208 -93.758)" fill="#fff" fill-rule="evenodd"/>
    <path id="Path_7893" data-name="Path 7893" d="M564.217,380.949s-3,2.576-4.182,3.621-3.546.54-5.157-.209c-4.042-1.88-9.06-4.178-10.1-4.664a47.211,47.211,0,0,1-4.827-2.642c-.513-.25-1.052-.494-1.584-.7-2.122-.82-2.508.348-1.185,1.741.533.56,2.53,2.182,3.34,2.8l-1.218,2.739s.324.492,2.756,1.842c.627.348,9.9,6.266,14.5,8.077s7.361,1.318,9.9-.905a45.542,45.542,0,0,0,4.878-5.013Z" transform="translate(-318.008 -95.351)" fill="#fff" fill-rule="evenodd"/>
    <path id="Path_7894" data-name="Path 7894" d="M391.261,554.526H399.6v22.648H376.932V554.526h4.853" transform="translate(-214.296 -211.396)" fill="#fff" fill-rule="evenodd"/>
    <path id="Path_7895" data-name="Path 7895" d="M479.2,577.174H467.521V554.526H490.19v22.648h-3.81" transform="translate(-273.208 -211.396)" fill="#fff" fill-rule="evenodd"/>
    <path id="Path_7896" data-name="Path 7896" d="M319.541,554.526h8.281v22.648H315.473" transform="translate(-174.328 -211.396)" fill="#fff" fill-rule="evenodd"/>
    <path id="Path_7897" data-name="Path 7897" d="M189.969,430.939l-4.131.271a2.625,2.625,0,0,1-2.793-2.444l-.585-8.869a2.624,2.624,0,0,1,2.447-2.79l4.131-.272a2.626,2.626,0,0,1,2.793,2.445l.584,8.87A2.624,2.624,0,0,1,189.969,430.939Z" transform="translate(-87.824 -121.849)" fill="#fff" fill-rule="evenodd"/>
    <path id="Path_7898" data-name="Path 7898" d="M127.393,477.622c-1.73,1.5-3.411.76-4.912-.968l-5.718-6.579c-1.5-1.728-2.335-3.628-.525-5.289,1.553-1.425,3.662-.548,5.164,1.179l5.718,6.58C128.621,474.273,129.122,476.121,127.393,477.622Z" transform="translate(-44.092 -152.573)" fill="#fff" fill-rule="evenodd"/>
    <path id="Path_7899" data-name="Path 7899" d="M176.768,498.554a31.876,31.876,0,1,1-33.9-29.684A31.857,31.857,0,0,1,176.768,498.554Z" transform="translate(-42.711 -155.647)" fill="#fff" fill-rule="evenodd"/>
    <path id="Path_7900" data-name="Path 7900" d="M183.844,419.779l-16.853,1.11a2.624,2.624,0,0,1-2.793-2.445l-.3-4.506a2.623,2.623,0,0,1,2.447-2.79l16.852-1.11a2.625,2.625,0,0,1,2.793,2.445l.3,4.507A2.624,2.624,0,0,1,183.844,419.779Z" transform="translate(-75.755 -117.429)" fill="#fff" fill-rule="evenodd"/>
    <path id="Path_7901" data-name="Path 7901" d="M204.951,525.992c-1.4-.425-1.3-1.633-.876-3.03l2.773-9.11c.426-1.4,1.215-2.393,2.416-2.027,1.251.38,1.408,1.663.983,3.059l-2.773,9.11C207.048,525.392,206.349,526.417,204.951,525.992Z" transform="translate(-101.703 -183.577)" fill="#fff" fill-rule="evenodd"/>
    <path id="Path_7902" data-name="Path 7902" d="M613.405,309.146s-1.513,3.5-.977,5.594c.862,3.359-.441,5.136-.766,6.16a.351.351,0,0,0,.326.457l1.479.041s.461,2.832,1.448,3.818c1.734,1.731,5.941-1.02,5.941-1.02l.3,6.122s.78,2.345,3.623.835,2.649-3.272,2.649-3.272l-2.739-14.916Z" transform="translate(-366.935 -51.821)" fill="#fff" fill-rule="evenodd"/>
    <path id="Path_7903" data-name="Path 7903" d="M662.092,626.752l1.078,8.526,4.133,1.346,1.976-.808V624.777Z" transform="translate(-399.741 -257.082)" fill="#fff" fill-rule="evenodd"/>
    <path id="Path_7904" data-name="Path 7904" d="M646.452,540.951s1.672,17.775,2.508,23.264,1.433,8.948,1.433,8.948,1.792,1.193,5.493,1.073a12.535,12.535,0,0,0,5.373-1.073l.119-16.583Z" transform="translate(-389.57 -202.568)" fill="#fff" fill-rule="evenodd"/>
    <path id="Path_7905" data-name="Path 7905" d="M626.63,651.458h21.74c1.167,0,1.077-3.5.448-5.116s-1.436-2.6-2.066-2.513,0,1.884-3.5,2.243-3.324-2.243-3.683-2.782-2.515-.718-3.863.718-5.839,3.231-7.546,4.039S626.63,651.458,626.63,651.458Z" transform="translate(-376.676 -268.864)" fill="#fff" fill-rule="evenodd"/>
    <path id="Path_7906" data-name="Path 7906" d="M724.395,608.1l4.851,7.629-1.707,3.859-3.324.717-1.707-1.885-4.671-6.372Z" transform="translate(-435.993 -246.234)" fill="#fff" fill-rule="evenodd"/>
    <path id="Path_7907" data-name="Path 7907" d="M636.3,455.659s-1.194,13.362-1.672,20.758.359,9.186,6.33,16.225,21.135,24.1,21.135,24.1a22.789,22.789,0,0,0,5.971-4.056,12.089,12.089,0,0,0,3.7-5.369s-8.477-11.93-12.538-17.775a81.453,81.453,0,0,0-5.612-7.516s1.91-9.782,3.1-16.225a59.827,59.827,0,0,0,1.194-9.663Z" transform="translate(-381.809 -147.101)" fill="#fff" fill-rule="evenodd"/>
    <path id="Path_7908" data-name="Path 7908" d="M708.763,636.894h9.612s1.168.09,2.7-1.795,5.928-7.987,6.556-8.8-1.393-3.568-2.995-4.308c-1.188-.549-1.948-.618-2.245-.448-.349.2.359,2.6-1.347,4.038a4.433,4.433,0,0,1-4.941.449c-.9-.629-1.8-.9-1.8.9s.158,4.28.158,4.28S707.82,632.2,708.763,636.894Z" transform="translate(-430.032 -254.928)" fill="#fff" fill-rule="evenodd"/>
    <path id="Path_7909" data-name="Path 7909" d="M698.564,399.09a99.411,99.411,0,0,1,7.851,9.435,93.838,93.838,0,0,1,7.851,14.324c1.479,3.3,2.162,6.252,1.024,7.73s-1.707.909-2.844-1.251a17.4,17.4,0,0,0-2.049-3.183l-2.616,1.25s.341-3.41-.228-4.433a71.788,71.788,0,0,0-7.965-10.117c-4.323-4.661-9.672-10.459-9.672-10.459Z" transform="translate(-417.835 -110.313)" fill="#fff" fill-rule="evenodd"/>
    <g id="mask1_0_560" data-name="mask1 0 560" transform="translate(130.479 265.64)">
      <path id="Path_7910" data-name="Path 7910" d="M376.013,396.9H398.7V374.216H376.013Z" transform="translate(-344.177 -359.777)" fill="#43d477" fill-rule="evenodd"/>
      <path id="Path_7911" data-name="Path 7911" d="M498.727,348.493c.532.206,1.073.451,1.586.7,0,0,.735.468,1.828,1.086l3.616-8.272-20.787-9.084-9.087,20.783,20.788,9.084,3.041-6.954-.048-.054,1.22-2.743c-.811-.618-2.81-2.242-3.343-2.8C496.216,348.842,496.6,347.671,498.727,348.493Z" transform="translate(-409.124 -332.924)" fill="#43d477" fill-rule="evenodd"/>
      <path id="Path_7912" data-name="Path 7912" d="M284.971,396.9h22.687V374.216H284.971Z" transform="translate(-284.971 -359.777)" fill="#f8f8f8" fill-rule="evenodd"/>
    </g>
    <g id="mask2_0_560" data-name="mask2 0 560" transform="translate(63.539 306.33)">
      <path id="Path_7913" data-name="Path 7913" d="M134.176,519.808a31.9,31.9,0,1,1,29.737-33.934A31.9,31.9,0,0,1,134.176,519.808Zm36.632-34.388a38.674,38.674,0,1,0-36.046,41.134A38.673,38.673,0,0,0,170.808,485.42Z" transform="translate(-93.542 -449.287)" fill="#43d477" fill-rule="evenodd"/>
      <path id="Path_7914" data-name="Path 7914" d="M195.591,544.536a5.451,5.451,0,1,0,3.629,6.8A5.45,5.45,0,0,0,195.591,544.536Z" transform="translate(-155.33 -511.075)" fill="#43d477" fill-rule="evenodd"/>
    </g>
    <path id="Path_7915" data-name="Path 7915" d="M648.673,371.407c-2.756-2.28-9.463-7.93-12.987-10.193-6.016-3.861-12.2-2.2-15.05,1.375-.043.01-.081.01-.125.025-3.986,1.329-9.511,6.158-9.511,6.158a24.932,24.932,0,0,0,4.267,5.668,20.116,20.116,0,0,0,2.948,2.636c-.192,7.812-.421,14.09-.52,16.669a1.224,1.224,0,0,0,1.227,1.27H643.76a1.746,1.746,0,0,0,1.671-2.249l-4.22-14.058,7.4-5.374A1.222,1.222,0,0,0,648.673,371.407Z" transform="translate(-366.515 -84.242)" fill="#5ae2e2" fill-rule="evenodd"/>
    <g id="mask3_0_560" data-name="mask3 0 560" transform="translate(244.485 274.758)">
      <path id="Path_7916" data-name="Path 7916" d="M648.673,371.407c-2.756-2.28-9.463-7.93-12.987-10.193-6.016-3.861-12.2-2.2-15.05,1.375-.043.01-.081.01-.125.025-3.986,1.329-9.511,6.158-9.511,6.158a24.932,24.932,0,0,0,4.267,5.668,20.116,20.116,0,0,0,2.948,2.636c-.192,7.812-.421,14.09-.52,16.669a1.224,1.224,0,0,0,1.227,1.27H643.76a1.746,1.746,0,0,0,1.671-2.249l-4.22-14.058,7.4-5.374A1.222,1.222,0,0,0,648.673,371.407Z" transform="translate(-611 -359)" fill="#ffcd4c" fill-rule="evenodd"/>
    </g>
    <g id="mask4_0_560" data-name="mask4 0 560" transform="translate(63.06 224.105)">
      <path id="Path_7917" data-name="Path 7917" d="M198.787,514.107l-2.668,8.761a5.605,5.605,0,0,0-2.462-.826l2.647-8.689a3.379,3.379,0,0,1,.787-1.465.977.977,0,0,1,.687-.3,1.169,1.169,0,0,1,.344.055C199.248,511.986,199.022,513.334,198.787,514.107Zm-.943,15.271a4.967,4.967,0,1,1-4.762-6.41,4.974,4.974,0,0,1,4.762,6.41Zm.557-18.65a1.919,1.919,0,0,0-1.968.463,4.25,4.25,0,0,0-1.046,1.883l-2.73,8.961a5.918,5.918,0,1,0,6.1,7.62,5.879,5.879,0,0,0-1.817-6.208l2.76-9.062C200.272,512.517,199.785,511.15,198.4,510.729Z" transform="translate(-153.944 -406.956)" fill="#1f3b64" fill-rule="evenodd"/>
      <path id="Path_7918" data-name="Path 7918" d="M350.922,251.072H219.986V234.125a9.418,9.418,0,0,1,9.414-9.4h10.893v6.36a8.639,8.639,0,1,0,10.506,0v-6.36h29.376v6.4a8.643,8.643,0,1,0,10.5-.073v-6.324H320.5v6.436a8.643,8.643,0,1,0,10.505-.145v-6.291h10.5a9.419,9.419,0,0,1,9.415,9.4Zm-70.269-16.822a.478.478,0,0,0,.479-.479V219.393a4.295,4.295,0,0,1,8.59,0V237.7a4.3,4.3,0,0,1-7.332,3.032.478.478,0,0,0-.677.677,5.254,5.254,0,0,0,8.967-3.709v-5.415a7.678,7.678,0,1,1-10.5.091v1.393A.478.478,0,0,0,280.653,234.251Zm45.1,8.7a5.255,5.255,0,0,0,5.252-5.246v-5.456a7.675,7.675,0,1,1-10.505.173V237.7A5.255,5.255,0,0,0,325.757,242.948Zm26.123-8.823a10.378,10.378,0,0,0-10.373-10.358h-10.5v-4.375a5.254,5.254,0,0,0-9.608-2.933.478.478,0,1,0,.793.535,4.3,4.3,0,0,1,7.856,2.4V237.7a4.295,4.295,0,0,1-8.59,0V222.474a.479.479,0,0,0-.957,0v1.293H290.679v-4.375a5.253,5.253,0,0,0-10.506,0v4.375H250.8v-4.375A5.255,5.255,0,0,0,242.672,215a.479.479,0,0,0,.526.8,4.292,4.292,0,0,1,6.643,3.591V237.7a4.245,4.245,0,0,1-.2,1.282.48.48,0,0,0,.915.286,5.264,5.264,0,0,0,.238-1.568v-5.37a7.681,7.681,0,1,1-10.506,0V237.7a5.256,5.256,0,0,0,6.893,4.984.478.478,0,0,0-.3-.909,4.3,4.3,0,0,1-5.636-4.075v-18.31a4.24,4.24,0,0,1,.466-1.945.479.479,0,1,0-.853-.434,5.186,5.186,0,0,0-.571,2.379v4.375H229.4a10.378,10.378,0,0,0-10.373,10.358v17.9H351.88Z" transform="translate(-174.669 -214.147)" fill="#1f3b64" fill-rule="evenodd"/>
      <path id="Path_7919" data-name="Path 7919" d="M268.9,579.488a.478.478,0,0,0-.479.478,6.663,6.663,0,0,1-6.661,6.65H144.149a6.58,6.58,0,0,1-2.946-.7,38.848,38.848,0,0,0,7.287-2.69.478.478,0,0,0-.421-.859A38.184,38.184,0,0,1,93.585,542.17a.479.479,0,0,0-.946-.148,39.125,39.125,0,0,0,38.651,45.154q1.292,0,2.6-.086a39.527,39.527,0,0,0,5.952-.853,7.579,7.579,0,0,0,4.306,1.337H261.765a7.621,7.621,0,0,0,7.619-7.607A.479.479,0,0,0,268.9,579.488Z" transform="translate(-92.173 -427.107)" fill="#1f3b64" fill-rule="evenodd"/>
      <path id="Path_7920" data-name="Path 7920" d="M112.164,401.786c-1.72-1.978-1.893-3.4-.564-4.625a2.1,2.1,0,0,1,1.451-.587c.043,0,.085,0,.128,0a4.5,4.5,0,0,1,2.9,1.725l.819.942a39.344,39.344,0,0,0-3.853,3.558Zm30.89-14.086.188,2.845a39.349,39.349,0,0,0-4.275.044,38.945,38.945,0,0,0-4.147.509l-.187-2.845Zm3.186-.21a4.164,4.164,0,0,0,.516-.085v3.447c-.842-.113-1.694-.19-2.55-.248l-.2-2.968Zm-38.113,22.663a.478.478,0,0,0,.657-.164,38.173,38.173,0,1,1,59.566,46.76.479.479,0,0,0,.673.681,39.573,39.573,0,0,0,4.383-5.133A39.066,39.066,0,0,0,147.715,391v-3.9a4.115,4.115,0,0,0,2.387-4.016l-.161-2.442a4.137,4.137,0,0,0-2.226-3.384V359.893a.479.479,0,0,0-.958,0v17.02a4.031,4.031,0,0,0-1.222-.12l-10.249.674a.479.479,0,0,0-.447.509.489.489,0,0,0,.51.446l10.249-.674a3.186,3.186,0,0,1,3.388,2.964l.16,2.441a3.183,3.183,0,0,1-2.969,3.383l-14.794.974A3.183,3.183,0,0,1,128,384.546l-.162-2.441a3.153,3.153,0,0,1,.783-2.305.479.479,0,0,0-.721-.631,4.107,4.107,0,0,0-1.018,3l.161,2.442a4.146,4.146,0,0,0,4.13,3.865c.092,0,.184,0,.276-.009l2.231-.147.2,2.954a39.238,39.238,0,0,0-16.221,7.376l-.845-.972a5.433,5.433,0,0,0-3.574-2.052,3.039,3.039,0,0,0-2.279.833c-1.731,1.589-1.566,3.592.487,5.956l.961,1.1a39.05,39.05,0,0,0-4.437,5.98A.477.477,0,0,0,108.127,410.154Z" transform="translate(-102.397 -308.617)" fill="#1f3b64" fill-rule="evenodd"/>
      <path id="Path_7921" data-name="Path 7921" d="M167.129,520.689a31.4,31.4,0,1,1-25.694-51.984q1.051-.07,2.087-.069a31.366,31.366,0,0,1,23.607,52.052Zm8.677-22.8a32.375,32.375,0,1,0-32.327,34.458q1.069,0,2.152-.07a32.348,32.348,0,0,0,30.176-34.388Z" transform="translate(-104.497 -379.024)" fill="#1f3b64" fill-rule="evenodd"/>
      <path id="Path_7922" data-name="Path 7922" d="M198.255,483.277h.031a.478.478,0,0,0,.446-.509l-.262-3.976a.466.466,0,0,0-.51-.445.478.478,0,0,0-.446.508l.262,3.976A.478.478,0,0,0,198.255,483.277Z" transform="translate(-160.678 -385.96)" fill="#1d2d3a" fill-rule="evenodd"/>
      <path id="Path_7923" data-name="Path 7923" d="M207.781,627.15a.478.478,0,0,0-.446.509l.263,3.975a.479.479,0,0,0,.478.447h.032a.479.479,0,0,0,.446-.509l-.263-3.975A.482.482,0,0,0,207.781,627.15Z" transform="translate(-167.064 -482.73)" fill="#1d2d3a" fill-rule="evenodd"/>
      <path id="Path_7924" data-name="Path 7924" d="M127.031,562.962l-3.981.262a.478.478,0,0,0,.031.956h.033l3.981-.262a.478.478,0,1,0-.064-.955Z" transform="translate(-111.962 -440.987)" fill="#1d2d3a" fill-rule="evenodd"/>
      <path id="Path_7925" data-name="Path 7925" d="M276.038,553.154l-3.981.262a.478.478,0,0,0,.031.955h.033l3.98-.262a.479.479,0,1,0-.064-.955Z" transform="translate(-208.864 -434.609)" fill="#1d2d3a" fill-rule="evenodd"/>
      <path id="Path_7926" data-name="Path 7926" d="M145.834,509.116a.478.478,0,0,0,.315-.838l-3-2.626a.478.478,0,1,0-.63.72l3,2.626A.48.48,0,0,0,145.834,509.116Z" transform="translate(-124.807 -403.641)" fill="#1d2d3a" fill-rule="evenodd"/>
      <path id="Path_7927" data-name="Path 7927" d="M255.458,603.938a.479.479,0,0,0-.631.72l3,2.625a.478.478,0,1,0,.63-.719Z" transform="translate(-197.844 -467.558)" fill="#1d2d3a" fill-rule="evenodd"/>
      <path id="Path_7928" data-name="Path 7928" d="M152.577,610.387l-2.63,3a.479.479,0,0,0,.721.631l2.629-3a.479.479,0,0,0-.72-.63Z" transform="translate(-129.668 -471.722)" fill="#1d2d3a" fill-rule="evenodd"/>
      <path id="Path_7929" data-name="Path 7929" d="M251.672,498.187a.479.479,0,0,0-.675.044l-2.629,3a.479.479,0,0,0,.72.631l2.63-3A.478.478,0,0,0,251.672,498.187Z" transform="translate(-193.672 -398.786)" fill="#1d2d3a" fill-rule="evenodd"/>
      <path id="Path_7930" data-name="Path 7930" d="M672.76,526.728c-.211.272-.848,1.14-1.658,2.244-1.548,2.111-3.887,5.3-4.9,6.546-1.313,1.62-2.289,1.618-2.324,1.618h-9.208a3.121,3.121,0,0,1,.553-2.232c1.381-1.965,4.774-2.494,4.809-2.5l.434-.065-.028-.437c0-.026-.157-2.492-.157-4.252,0-.772.179-.887.181-.888s.2-.084.863.382a4.889,4.889,0,0,0,5.527-.475,4.6,4.6,0,0,0,1.4-3.662c0-.115,0-.247.012-.348a4.075,4.075,0,0,1,1.68.487,6.455,6.455,0,0,1,2.8,3.245C672.8,526.616,672.771,526.715,672.76,526.728Zm-13.186-3.086a25.909,25.909,0,0,0,2.544-2.062,16.677,16.677,0,0,0,2.247-2.512l2.9,4.559a3.16,3.16,0,0,1-1.027,2.312,3.986,3.986,0,0,1-4.36.423,4.517,4.517,0,0,0-.556-.337Zm-21.444,7.073c.257.957.8,2.945,4.016,2.617,2.748-.281,3.256-1.454,3.529-2.084.021-.049.048-.108.07-.154.279.08.9.724,1.479,2.205.515,1.323.576,3.69.113,4.367-.044.065-.084.1-.115.1H625.948a2.851,2.851,0,0,1,1.252-2.5c.329-.156.761-.348,1.255-.568,2.105-.935,5.286-2.349,6.44-3.577a3.149,3.149,0,0,1,2.266-.962h.015a1.5,1.5,0,0,1,.833.18A2.567,2.567,0,0,1,638.13,530.716Zm3.191-2.914c.173,0,.352,0,.534-.009a19.392,19.392,0,0,0,2.73-.274v3.764c-.289.447-.885.93-2.536,1.1-2.427.248-2.748-1-2.993-1.914-.029-.11-.057-.205-.087-.3l-.333-2.628A13.47,13.47,0,0,0,641.32,527.8Zm29.022-5.521c-.709-.328-2-.818-2.686-.431a.576.576,0,0,0-.229.25l-2.484-3.9a8.551,8.551,0,0,0,1.019-2.225l.051-.211-.127-.177c-.084-.119-8.528-12-12.543-17.775-3.355-4.828-5-6.813-5.494-7.385.3-1.522,1.982-10.176,3.061-16,.343-1.842.62-3.6.826-5.233a.479.479,0,0,0-.95-.12c-.2,1.612-.479,3.354-.816,5.178-1.181,6.367-3.087,16.125-3.106,16.223l-.047.239.166.178a83.113,83.113,0,0,1,5.572,7.465c3.737,5.378,11.306,16.033,12.414,17.591a12.558,12.558,0,0,1-3.5,4.933,23.494,23.494,0,0,1-5.528,3.826c-1.947-2.191-15.409-17.352-20.905-23.828-6.042-7.12-6.681-8.753-6.221-15.889.274-4.231.782-10.419,1.161-14.865a.478.478,0,0,0-.437-.517.491.491,0,0,0-.517.435c-.379,4.451-.889,10.646-1.163,14.886-.44,6.816.3,9.069,4.563,14.309.2,2.021.441,4.433.7,6.872.569,5.471,1.013,9.259,1.318,11.257.826,5.43,1.428,8.924,1.434,8.959l.036.2.172.114a6.6,6.6,0,0,0,1.562.659l.251,1.984a3.245,3.245,0,0,0-.425-.069,4.1,4.1,0,0,0-3.267,1.253c-1.021,1.087-4.219,2.509-6.13,3.358-.5.223-.942.419-1.276.577-1.982.936-1.81,3.756-1.8,3.876l.031.445h22.2a1.071,1.071,0,0,0,.907-.514c.752-1.1.516-3.9-.012-5.253-.743-1.909-1.678-2.933-2.573-2.813v-2.819a8.31,8.31,0,0,0,1.911-.665l.24-.137.05-6.96a.479.479,0,0,0-.474-.482h0a.478.478,0,0,0-.479.475l-.047,6.385a13.823,13.823,0,0,1-4.915.9,10.454,10.454,0,0,1-5.047-.9c-.144-.85-.686-4.073-1.394-8.718-.3-1.984-.744-5.756-1.313-11.213-.2-1.892-.386-3.758-.554-5.45.256.3.511.609.788.936,5.91,6.963,21,23.941,21.155,24.111l.228.255.315-.131a18.711,18.711,0,0,0,2.781-1.544l1.263,1.72c-.009,0-.017.006-.025.01-.7.362-.7,1.4-.7,1.736,0,1.348.089,3.1.134,3.89-1.047.221-3.705.948-5.017,2.813a4.18,4.18,0,0,0-.65,3.356l.078.384h9.973a.611.611,0,0,0,.064,0c.315,0,1.546-.13,3.042-1.974,1.024-1.263,3.279-4.338,4.926-6.582.8-1.094,1.432-1.952,1.642-2.222a1.292,1.292,0,0,0,.16-1.142A7.262,7.262,0,0,0,670.342,522.28Z" transform="translate(-438.668 -379.656)" fill="#1f3b64" fill-rule="evenodd"/>
      <path id="Path_7931" data-name="Path 7931" d="M618.774,321.376c-.106.2-.214.4-.3.6-.37.506-1.142,2.5-1.318,10.332q-.017.777-.036,1.537a19.733,19.733,0,0,1-2.132-1.989,28.833,28.833,0,0,1-4.005-5.2A41.782,41.782,0,0,1,618.774,321.376Zm-4.816-6.4a4.336,4.336,0,0,1-.813-1.571,2.915,2.915,0,0,0,3.1-1.185.479.479,0,0,0-.757-.586,2,2,0,0,1-2.632.655c-.009,0-.018,0-.028-.009-.084-.349-.146-.658-.179-.862l-.064-.39-1.7-.047c.052-.136.117-.285.188-.448a8.156,8.156,0,0,0,.529-5.817,8.423,8.423,0,0,1,.517-4.1,23.236,23.236,0,0,1,3.744,2.978c.831.779,2.379,1.986,3.4,1.317.159-.1.324-.228.493-.357a5.342,5.342,0,0,1,.743-.506,1.9,1.9,0,0,1,1.457-.136,2.237,2.237,0,0,1,1.184,1.387,2.856,2.856,0,0,1-.823,2.927,7.348,7.348,0,0,1-1.293.96l-.34.19.116.372a31.192,31.192,0,0,0,3.368,7.212,10.2,10.2,0,0,0-3.865,2.382l-.291-5.91-.7.456C618.221,314.6,615.153,316.165,613.958,314.974Zm20.781,4.45c3.123,2,8.848,6.747,11.924,9.3l.978.81a.736.736,0,0,1-.039,1.164l-7.37,5.342-2.166,1.426a.237.237,0,0,1-.342-.095l-.077-.115a29.323,29.323,0,0,1-3.695-5.437,60.4,60.4,0,0,1-2.693-5.792.479.479,0,0,0-.893.346,61.375,61.375,0,0,0,2.74,5.895,30.136,30.136,0,0,0,3.79,5.587,1.194,1.194,0,0,0,1.7.409l1.64-1.078,4.024,13.373a1.259,1.259,0,0,1-1.208,1.621H618.28a.738.738,0,0,1-.54-.228.727.727,0,0,1-.2-.539c.106-2.753.375-10.126.577-19.078.183-8.067,1-9.591,1.112-9.764l.107-.162a8.513,8.513,0,0,1,5.392-4.634A11.841,11.841,0,0,1,634.739,319.424Zm34.488,34.583,8.258,20.067-20.094,8.247-8.258-20.067,9.089-3.73a20.243,20.243,0,0,1,1.632,2.64c.786,1.492,1.323,2.261,2.072,2.325.031,0,.062,0,.093,0a2.1,2.1,0,0,0,1.484-1.01c.922-1.2.87-3.245-.118-6.079ZM613.789,323.27c-2.038,1.452-3.6,2.807-3.723,2.912l-.266.233.144.323a25.031,25.031,0,0,0,4.34,5.769,20.45,20.45,0,0,0,2.805,2.529c-.191,7.693-.417,13.87-.512,16.336a1.677,1.677,0,0,0,.472,1.24,1.7,1.7,0,0,0,1.231.522h24.768a2.217,2.217,0,0,0,2.125-2.854l-4.111-13.662,6.076-4.4c.583.639,1.343,1.489,2.206,2.495,1.778,2.069,3.294,3.987,4.506,5.7a93.623,93.623,0,0,1,7.812,14.246,15.388,15.388,0,0,1,1.547,5.288l-.977-2.492a.479.479,0,0,0-.892.35l1.516,3.863c0,.006.009.008.011.014a1.891,1.891,0,0,1-.123.223l-.012.015-1.954-4.113a.479.479,0,0,0-.866.41l2.042,4.3c-.374-.133-1.007-1.328-1.254-1.795a20.8,20.8,0,0,0-1.781-2.861.468.468,0,0,0-.068-.17c-.422-.635-.89-1.355-.965-1.489a.479.479,0,0,0-.891.346,6.3,6.3,0,0,0,.6.983l-1.448.691a9.3,9.3,0,0,0-.351-3.878,71.723,71.723,0,0,0-8.037-10.212l-3.812-4.115a.479.479,0,0,0-.7.65l3.813,4.115a72.48,72.48,0,0,1,7.9,10.026,10.612,10.612,0,0,1,.187,3.943l-7.267,2.983,8.985,21.836,21.866-8.973-8.985-21.836-6.693,2.746c-.16-.4-.328-.811-.52-1.241a94.611,94.611,0,0,0-7.9-14.407c-1.23-1.737-2.765-3.679-4.561-5.771-.833-.972-1.574-1.8-2.153-2.437l.246-.179a1.69,1.69,0,0,0,.086-2.675l-.977-.81c-1.336-1.108-3.17-2.629-5.078-4.163a5.674,5.674,0,0,0,2.123-3.341c1.034-4.477-4.154-6.993-5.859-7.82-2.9-1.409-3.569-2.919-4.092-6.738-.41-3-2.56-4.042-4.287-4.877a7.937,7.937,0,0,1-1.737-1.011,15.219,15.219,0,0,1-1.473-1.629,11.639,11.639,0,0,0-4.627-3.876c-4.224-1.662-8.033-.269-9.785,1.728A5.752,5.752,0,0,0,611,299.766l0,.349.234.076a9.525,9.525,0,0,0-.576,4.761,7.211,7.211,0,0,1-.479,5.2c-.115.265-.215.5-.279.7a.828.828,0,0,0,.77,1.08l1.092.03a8.327,8.327,0,0,0,1.512,3.689,2.481,2.481,0,0,0,1.618.688,16.589,16.589,0,0,1-.284,3.39C614.391,320.9,614.077,322.181,613.789,323.27Z" transform="translate(-428.795 -265.943)" fill="#1f3b64" fill-rule="evenodd"/>
      <path id="Path_7932" data-name="Path 7932" d="M596.689,445.97a.478.478,0,0,0-.479.478v64.441a.479.479,0,0,0,.957,0V446.448A.478.478,0,0,0,596.689,445.97Z" transform="translate(-419.957 -364.906)" fill="#1f3b64" fill-rule="evenodd"/>
      <path id="Path_7933" data-name="Path 7933" d="M376.092,485.291h21.725V463.6H376.092Zm-.958.957h23.641V462.64H375.134Z" transform="translate(-276.187 -375.746)" fill="#1f3b64" fill-rule="evenodd"/>
      <path id="Path_7934" data-name="Path 7934" d="M466.738,485.291h21.725V463.6H466.738Zm-.957.957h23.64V462.64h-23.64Z" transform="translate(-335.137 -375.746)" fill="#1f3b64" fill-rule="evenodd"/>
      <path id="Path_7935" data-name="Path 7935" d="M307.75,462.639H284.588a.479.479,0,1,0,0,.957h22.2V485.29H296.549a.479.479,0,1,0,0,.957h11.2Z" transform="translate(-216.992 -375.746)" fill="#1f3b64" fill-rule="evenodd"/>
      <path id="Path_7936" data-name="Path 7936" d="M376.092,395.147h21.725v-21.7H376.092Zm-.958.957h23.641V372.5H375.134Z" transform="translate(-276.187 -317.124)" fill="#1f3b64" fill-rule="evenodd"/>
      <path id="Path_7937" data-name="Path 7937" d="M285.066,395.147h21.725v-21.7H285.066Zm-.957.957H307.75V372.5H284.109Z" transform="translate(-216.992 -317.124)" fill="#1f3b64" fill-rule="evenodd"/>
      <path id="Path_7938" data-name="Path 7938" d="M380.469,554.375a.478.478,0,1,0,0-.957h-5.336v23.609h23.641V553.418H389.95a.478.478,0,1,0,0,.957h7.866v21.695H376.091V554.375Z" transform="translate(-276.187 -434.781)" fill="#1f3b64" fill-rule="evenodd"/>
      <path id="Path_7939" data-name="Path 7939" d="M485.13,576.07a.479.479,0,1,0,0,.957h4.291V553.419h-23.64v23.608h12.163a.479.479,0,0,0,0-.957H466.739V554.376h21.725V576.07Z" transform="translate(-335.137 -434.781)" fill="#1f3b64" fill-rule="evenodd"/>
      <path id="Path_7940" data-name="Path 7940" d="M314.119,576.07a.479.479,0,1,0,0,.957h12.836V553.419h-8.765a.478.478,0,1,0,0,.957H326V576.07Z" transform="translate(-236.197 -434.781)" fill="#1f3b64" fill-rule="evenodd"/>
      <path id="Path_7941" data-name="Path 7941" d="M490.015,360.653l-19.906-8.689,8.7-19.879,19.907,8.689-3.172,7.246a25.724,25.724,0,0,0-3.053-1.463c-1.6-.617-2.209-.152-2.433.18-.387.578-.116,1.452.727,2.337a26.18,26.18,0,0,0,3.161,2.6Zm-1.551,7.4H466.739v-21.7h4.78l-2.671,6.1,19.615,8.562Zm33.94-6.6a.479.479,0,0,0-.677-.008c-.5.486-1.044.994-1.624,1.5-2.562,2.246-5.2,2.475-9.41.82-3.812-1.5-10.842-5.835-13.488-7.466-.515-.318-.855-.527-.959-.585a19.132,19.132,0,0,1-2.263-1.451l.878-1.934c.3.211.5.347.536.369a.478.478,0,1,0,.535-.793,36.986,36.986,0,0,1-4.453-3.484c-.621-.653-.668-1.083-.625-1.146.029-.041.367-.177,1.291.18a29.777,29.777,0,0,1,4.378,2.255.462.462,0,0,0,.236.067,13.826,13.826,0,0,0,1.77,1.008c.372.173,1.252.578,2.412,1.112,2.092.961,5.1,2.345,7.7,3.553,1.762.82,4.307,1.346,5.68.135a.479.479,0,1,0-.634-.718c-.957.844-3.077.444-4.642-.283-2.6-1.211-5.611-2.594-7.7-3.557-1.157-.532-2.034-.936-2.407-1.109a13.963,13.963,0,0,1-2.5-1.547l3.538-8.085-21.661-9.454L471.938,345.4h-6.156v23.608h23.64v-7.568l1.087.475,3.068-6.761a20.836,20.836,0,0,0,2.2,1.393c.1.056.427.258.92.562,2.666,1.645,9.749,6.012,13.641,7.543a14.024,14.024,0,0,0,5.067,1.143,7.754,7.754,0,0,0,5.326-2.134c.591-.519,1.148-1.039,1.659-1.536A.477.477,0,0,0,522.4,361.446Z" transform="translate(-335.137 -290.025)" fill="#1f3b64" fill-rule="evenodd"/>
      <path id="Path_7942" data-name="Path 7942" d="M617.548,330.058a.827.827,0,1,0,.622.8A.73.73,0,0,0,617.548,330.058Z" transform="translate(-433.429 -289.526)" fill="#393f49" fill-rule="evenodd"/>
      <path id="Path_7943" data-name="Path 7943" d="M617.171,321.787a1.856,1.856,0,0,0-1.9.071.478.478,0,0,0,.529.8.925.925,0,0,1,.893-.042.478.478,0,1,0,.48-.826Z" transform="translate(-432.212 -283.997)" fill="#393f49" fill-rule="evenodd"/>
      <path id="Path_7944" data-name="Path 7944" d="M638.77,328.514a.5.5,0,0,0,.142-.021c.591-.183,1.6-.763,1.62-1.77a1.153,1.153,0,0,0-.363-.94,1.2,1.2,0,0,0-.923-.257.473.473,0,0,0-.4.534.479.479,0,0,0,.533.413.257.257,0,0,1,.156.029c.016.014.042.072.038.2-.013.565-.939.875-.949.879a.478.478,0,0,0,.143.934Z" transform="translate(-447.324 -286.571)" fill="#393f49" fill-rule="evenodd"/>
    </g>
    <path id="Path_7947" data-name="Path 7947" d="M613.186,418.759l6.783,3.915-6.783,3.915Zm-.508-.881v9.592l8.307-4.8Z" transform="translate(-344.758 -173.929)" fill="#1f3b64" fill-rule="evenodd" opacity="0.1"/>
    <path id="Path_7948" data-name="Path 7948" d="M350.053,238.591l7.3,4.216-7.3,4.216Zm-.637,9.534,9.212-5.318-9.212-5.318Z" transform="matrix(0.899, -0.438, 0.438, 0.899, -299.422, 155.999)" fill="#1f3b64" fill-rule="evenodd" opacity="0.3"/>
    <path id="Path_7949" data-name="Path 7949" d="M425.075,325.251a6.091,6.091,0,1,1,6.092-6.091A6.1,6.1,0,0,1,425.075,325.251Zm0-13.108a7.017,7.017,0,1,0,7.018,7.017A7.025,7.025,0,0,0,425.075,312.143Z" transform="translate(-200.034 -121.048)" fill="#43d477" fill-rule="evenodd" opacity="0.5"/>
    <path id="Path_7951" data-name="Path 7951" d="M412.134,226.345a1.917,1.917,0,1,1,1.917-1.916A1.919,1.919,0,0,1,412.134,226.345Zm2.843-1.916a2.842,2.842,0,1,0-2.843,2.842A2.845,2.845,0,0,0,414.977,224.429Z" transform="translate(-233.417 -13.014)" fill="#1f3b64" fill-rule="evenodd" opacity="0.5"/>
    <path id="Path_7952" data-name="Path 7952" d="M209.918,394.376a2.694,2.694,0,1,1,2.695-2.694A2.7,2.7,0,0,1,209.918,394.376Zm0-5.855a3.16,3.16,0,1,0,3.161,3.161A3.165,3.165,0,0,0,209.918,388.521Z" transform="translate(-118.386 -113.763)" fill="#43d477" fill-rule="evenodd" opacity="0.6"/>
  </g>
</svg>
