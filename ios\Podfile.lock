PODS:
  - add_2_calendar (0.0.1):
    - Flutter
  - app_links (0.0.2):
    - Flutter
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - audio_session (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
    - FlutterMacOS
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - FBAEMKit (17.1.0):
    - FBSDKCoreKit_Basics (= 17.1.0)
  - FBSDKCoreKit (17.1.0):
    - FBAEMKit (= 17.1.0)
    - FBSDKCoreKit_Basics (= 17.1.0)
  - FBSDKCoreKit_Basics (17.1.0)
  - FBSDKLoginKit (17.1.0):
    - FBSDKCoreKit (= 17.1.0)
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Firebase/CoreOnly (11.6.0):
    - FirebaseCore (~> 11.6.0)
  - Firebase/Messaging (11.6.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.6.0)
  - firebase_core (3.10.0):
    - Firebase/CoreOnly (= 11.6.0)
    - Flutter
  - firebase_messaging (15.2.0):
    - Firebase/Messaging (= 11.6.0)
    - firebase_core
    - Flutter
  - FirebaseCore (11.6.0):
    - FirebaseCoreInternal (~> 11.6.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreInternal (11.6.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseInstallations (11.6.0):
    - FirebaseCore (~> 11.6.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.6.0):
    - FirebaseCore (~> 11.6.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - Flutter (1.0.0)
  - flutter_facebook_auth (7.1.1):
    - FBSDKLoginKit (~> 17.1.0)
    - Flutter
  - flutter_image_compress_common (1.0.0):
    - Flutter
    - Mantle
    - SDWebImage
    - SDWebImageWebPCoder
  - flutter_inappwebview_ios (0.0.1):
    - Flutter
    - flutter_inappwebview_ios/Core (= 0.0.1)
    - OrderedSet (~> 6.0.3)
  - flutter_inappwebview_ios/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 6.0.3)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_native_splash (2.4.3):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - flutter_tts (0.0.1):
    - Flutter
  - google_sign_in_ios (0.0.1):
    - AppAuth (>= 1.7.4)
    - Flutter
    - FlutterMacOS
    - GoogleSignIn (~> 7.1)
    - GTMSessionFetcher (>= 3.4.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleSignIn (7.1.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUtilities/AppDelegateSwizzler (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.0.2):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.0.2):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.0.2)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.0.2)
  - GoogleUtilities/Reachability (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher (3.5.0):
    - GTMSessionFetcher/Full (= 3.5.0)
  - GTMSessionFetcher/Core (3.5.0)
  - GTMSessionFetcher/Full (3.5.0):
    - GTMSessionFetcher/Core
  - image_picker_ios (0.0.1):
    - Flutter
  - just_audio (0.0.1):
    - Flutter
  - libwebp (1.3.2):
    - libwebp/demux (= 1.3.2)
    - libwebp/mux (= 1.3.2)
    - libwebp/sharpyuv (= 1.3.2)
    - libwebp/webp (= 1.3.2)
  - libwebp/demux (1.3.2):
    - libwebp/webp
  - libwebp/mux (1.3.2):
    - libwebp/demux
  - libwebp/sharpyuv (1.3.2)
  - libwebp/webp (1.3.2):
    - libwebp/sharpyuv
  - Mantle (2.2.0):
    - Mantle/extobjc (= 2.2.0)
  - Mantle/extobjc (2.2.0)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - open_file_ios (0.0.1):
    - Flutter
  - OrderedSet (6.0.3)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - SDWebImage (5.20.0):
    - SDWebImage/Core (= 5.20.0)
  - SDWebImage/Core (5.20.0)
  - SDWebImageWebPCoder (0.14.6):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - SwiftyGif (5.4.5)
  - syncfusion_flutter_pdfviewer (0.0.1):
    - Flutter
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - add_2_calendar (from `.symlinks/plugins/add_2_calendar/ios`)
  - app_links (from `.symlinks/plugins/app_links/ios`)
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/darwin`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_facebook_auth (from `.symlinks/plugins/flutter_facebook_auth/ios`)
  - flutter_image_compress_common (from `.symlinks/plugins/flutter_image_compress_common/ios`)
  - flutter_inappwebview_ios (from `.symlinks/plugins/flutter_inappwebview_ios/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - flutter_tts (from `.symlinks/plugins/flutter_tts/ios`)
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/darwin`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - just_audio (from `.symlinks/plugins/just_audio/ios`)
  - open_file_ios (from `.symlinks/plugins/open_file_ios/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - syncfusion_flutter_pdfviewer (from `.symlinks/plugins/syncfusion_flutter_pdfviewer/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - AppAuth
    - DKImagePickerController
    - DKPhotoGallery
    - FBAEMKit
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - FBSDKLoginKit
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - GoogleDataTransport
    - GoogleSignIn
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - libwebp
    - Mantle
    - nanopb
    - OrderedSet
    - PromisesObjC
    - SDWebImage
    - SDWebImageWebPCoder
    - SwiftyGif

EXTERNAL SOURCES:
  add_2_calendar:
    :path: ".symlinks/plugins/add_2_calendar/ios"
  app_links:
    :path: ".symlinks/plugins/app_links/ios"
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/darwin"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_facebook_auth:
    :path: ".symlinks/plugins/flutter_facebook_auth/ios"
  flutter_image_compress_common:
    :path: ".symlinks/plugins/flutter_image_compress_common/ios"
  flutter_inappwebview_ios:
    :path: ".symlinks/plugins/flutter_inappwebview_ios/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  flutter_tts:
    :path: ".symlinks/plugins/flutter_tts/ios"
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/darwin"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  just_audio:
    :path: ".symlinks/plugins/just_audio/ios"
  open_file_ios:
    :path: ".symlinks/plugins/open_file_ios/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  syncfusion_flutter_pdfviewer:
    :path: ".symlinks/plugins/syncfusion_flutter_pdfviewer/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  add_2_calendar: 5eee66d5a3b99cd5e1487a7e03abd4e3ac4aff11
  app_links: e7a6750a915a9e161c58d91bc610e8cd1d4d0ad0
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  audio_session: 088d2483ebd1dc43f51d253d4a1c517d9a2e7207
  connectivity_plus: 18382e7311ba19efcaee94442b23b32507b20695
  device_info_plus: c6fb39579d0f423935b0c9ce7ee2f44b71b9fce6
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  FBAEMKit: cb719c53575a3be86ea873279f30d6a2c4e15881
  FBSDKCoreKit: ecdb980a24633ccb012700299ceb16d0235e14d2
  FBSDKCoreKit_Basics: 045101c4a9ef10c845347424d73a29aae02c3e43
  FBSDKLoginKit: 69eb59b2f839aba635616df6e422acd0ca88030a
  file_picker: 09aa5ec1ab24135ccd7a1621c46c84134bfd6655
  Firebase: 374a441a91ead896215703a674d58cdb3e9d772b
  firebase_core: feb37e79f775c2bd08dd35e02d83678291317e10
  firebase_messaging: e2f0ba891b1509668c07f5099761518a5af8fe3c
  FirebaseCore: 48b0dd707581cf9c1a1220da68223fb0a562afaa
  FirebaseCoreInternal: d98ab91e2d80a56d7b246856a8885443b302c0c2
  FirebaseInstallations: efc0946fc756e4d22d8113f7c761948120322e8c
  FirebaseMessaging: e1aca1fcc23e8b9eddb0e33f375ff90944623021
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_facebook_auth: 48be46091bd1a5cc5a66055f1228a9d99a28bfaa
  flutter_image_compress_common: ec1d45c362c9d30a3f6a0426c297f47c52007e3e
  flutter_inappwebview_ios: 6f63631e2c62a7c350263b13fa5427aedefe81d4
  flutter_local_notifications: df98d66e515e1ca797af436137b4459b160ad8c9
  flutter_native_splash: f71420956eb811e6d310720fee915f1d42852e7a
  flutter_secure_storage: d33dac7ae2ea08509be337e775f6b59f1ff45f12
  flutter_tts: 0f492aab6accf87059b72354fcb4ba934304771d
  google_sign_in_ios: 07375bfbf2620bc93a602c0e27160d6afc6ead38
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleSignIn: d4281ab6cf21542b1cfaff85c191f230b399d2db
  GoogleUtilities: 26a3abef001b6533cf678d3eb38fd3f614b7872d
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  image_picker_ios: c560581cceedb403a6ff17f2f816d7fea1421fc1
  just_audio: baa7252489dbcf47a4c7cc9ca663e9661c99aafa
  libwebp: 1786c9f4ff8a279e4dac1e8f385004d5fc253009
  Mantle: c5aa8794a29a022dfbbfc9799af95f477a69b62d
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  open_file_ios: 461db5853723763573e140de3193656f91990d9e
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  package_info_plus: c0502532a26c7662a62a356cebe2692ec5fe4ec4
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  SDWebImage: 73c6079366fea25fa4bb9640d5fb58f0893facd8
  SDWebImageWebPCoder: e38c0a70396191361d60c092933e22c20d5b1380
  share_plus: 8b6f8b3447e494cca5317c8c3073de39b3600d1f
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sqflite_darwin: 5a7236e3b501866c1c9befc6771dfd73ffb8702d
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  syncfusion_flutter_pdfviewer: bb9998884b864cfedf72628df3503bdf57e397c0
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  video_player_avfoundation: 7c6c11d8470e1675df7397027218274b6d2360b3
  wakelock_plus: 373cfe59b235a6dd5837d0fb88791d2f13a90d56
  webview_flutter_wkwebview: 0982481e3d9c78fd5c6f62a002fcd24fc791f1e4

PODFILE CHECKSUM: 83f6a08c58d4b249cda5d50acce1e33fac59623c

COCOAPODS: 1.15.2
