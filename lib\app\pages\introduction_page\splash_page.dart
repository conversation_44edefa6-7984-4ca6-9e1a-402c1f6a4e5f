import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:flutter_svg/svg.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:webinar/app/pages/introduction_page/intro_page.dart';
import 'package:webinar/app/pages/main_page/main_page.dart';
import 'package:webinar/app/pages/offline_page/internet_connection_page.dart';
import 'package:webinar/app/services/guest_service/guest_service.dart';
import 'package:webinar/common/common.dart';
import 'package:webinar/common/data/app_data.dart';
import 'package:webinar/common/utils/app_text.dart';
import 'package:webinar/config/assets.dart';
import 'package:webinar/config/colors.dart';
import 'package:webinar/config/styles.dart';

class SplashPage extends StatefulWidget {
  static const String pageName = '/splash';
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> with TickerProviderStateMixin {
  late AnimationController animationController;
  late AnimationController fadeController;
  late AnimationController scaleController;
  late AnimationController backgroundController;

  late Animation<double> fadeAnimation;
  late Animation<double> scaleAnimation;
  late Animation<double> backgroundAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    animationController =
        AnimationController(vsync: this, duration: const Duration(seconds: 3));
    fadeController = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 1500));
    scaleController = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 1200));
    backgroundController =
        AnimationController(vsync: this, duration: const Duration(seconds: 4));

    // Initialize animations
    fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: fadeController, curve: Curves.easeInOut));

    scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
        CurvedAnimation(parent: scaleController, curve: Curves.elasticOut));

    backgroundAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: backgroundController, curve: Curves.easeInOut));

    FlutterNativeSplash.remove();

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      // Start animations with staggered timing
      backgroundController.forward();

      Timer(const Duration(milliseconds: 300), () {
        if (mounted) {
          scaleController.forward();
        }
      });

      Timer(const Duration(milliseconds: 600), () {
        if (mounted) {
          fadeController.forward();
          animationController.forward();
        }
      });

      Timer(const Duration(seconds: 3), () async {
        final List<ConnectivityResult> connectivityResult =
            await (Connectivity().checkConnectivity());

        if (connectivityResult.contains(ConnectivityResult.none)) {
          nextRoute(InternetConnectionPage.pageName, isClearBackRoutes: true);
        } else {
          String token = await AppData.getAccessToken();

          if (mounted) {
            if (token.isEmpty) {
              bool isFirst = await AppData.getIsFirst();

              if (isFirst) {
                nextRoute(IntroPage.pageName, isClearBackRoutes: true);
              } else {
                nextRoute(MainPage.pageName, isClearBackRoutes: true);
              }
            } else {
              nextRoute(MainPage.pageName, isClearBackRoutes: true);
            }
          }
        }
      });
    });

    GuestService.config();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: green77(),
      body: AnimatedBuilder(
        animation: backgroundAnimation,
        builder: (context, child) {
          return Container(
            width: getSize().width,
            height: getSize().height,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  green77(),
                  Color.lerp(green77(), green91(), backgroundAnimation.value) ??
                      green77(),
                  green91(),
                ],
                stops: [0.0, 0.5, 1.0],
              ),
              image: const DecorationImage(
                image: AssetImage(AppAssets.splashPng),
                fit: BoxFit.cover,
                opacity: 0.1,
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Spacer(),
                const Spacer(),

                // Logo with enhanced animations
                AnimatedBuilder(
                  animation: scaleAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: scaleAnimation.value,
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          // Rotating outer ring
                          AnimatedBuilder(
                            animation: animationController,
                            builder: (context, _) {
                              return Transform.rotate(
                                angle: animationController.value * 2 * 3.14159,
                                child: Container(
                                  width: 120,
                                  height: 120,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: Colors.white.withOpacity(0.3),
                                      width: 2,
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),

                          // Main logo
                          Center(
                            child: AnimatedBuilder(
                              animation: animationController,
                              builder: (content, _) {
                                return Transform.rotate(
                                  angle: animationController.value * .5 * 3.14,
                                  child: SvgPicture.asset(
                                    AppAssets.whiteLogoEmptySvg,
                                    width: 80,
                                    height: 80,
                                  ),
                                );
                              },
                            ),
                          ),

                          // Inner logo
                          Center(
                            child: SvgPicture.asset(
                              AppAssets.logoLineSvg,
                              width: 60,
                              height: 60,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),

                space(50),

                // Title with fade animation
                AnimatedBuilder(
                  animation: fadeAnimation,
                  builder: (context, child) {
                    return Opacity(
                      opacity: fadeAnimation.value,
                      child: Transform.translate(
                        offset: Offset(0, 20 * (1 - fadeAnimation.value)),
                        child: Text(
                          appText.webinar,
                          style: style24Bold().copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w700,
                            letterSpacing: 1.2,
                          ),
                        ),
                      ),
                    );
                  },
                ),

                space(15),

                // Description with fade animation
                AnimatedBuilder(
                  animation: fadeAnimation,
                  builder: (context, child) {
                    return Opacity(
                      opacity: fadeAnimation.value * 0.9,
                      child: Transform.translate(
                        offset: Offset(0, 30 * (1 - fadeAnimation.value)),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 40),
                          child: Text(
                            appText.splashDesc,
                            style: style16Regular().copyWith(
                              color: Colors.white.withOpacity(0.9),
                              height: 1.4,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    );
                  },
                ),

                const Spacer(),
                const Spacer(),

                // Enhanced loading indicator
                AnimatedBuilder(
                  animation: fadeAnimation,
                  builder: (context, child) {
                    return Opacity(
                      opacity: fadeAnimation.value,
                      child: Column(
                        children: [
                          const SizedBox(
                            width: 40,
                            height: 40,
                            child: LoadingIndicator(
                              indicatorType: Indicator.ballPulse,
                              colors: [Colors.white],
                              strokeWidth: 2,
                              backgroundColor: Colors.transparent,
                              pathBackgroundColor: Colors.transparent,
                            ),
                          ),
                          space(20),
                          Text(
                            'Loading...',
                            style: style12Regular().copyWith(
                              color: Colors.white.withOpacity(0.7),
                              letterSpacing: 1.5,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),

                const Spacer(),
              ],
            ),
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    animationController.dispose();
    fadeController.dispose();
    scaleController.dispose();
    backgroundController.dispose();
    super.dispose();
  }
}
